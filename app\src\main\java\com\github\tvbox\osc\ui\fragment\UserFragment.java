package com.github.tvbox.osc.ui.fragment;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.animation.BounceInterpolator;
import android.widget.LinearLayout;
import android.widget.Toast;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.github.tvbox.osc.R;
import com.github.tvbox.osc.api.ApiConfig;
import com.github.tvbox.osc.base.BaseLazyFragment;
import com.github.tvbox.osc.bean.Movie;
import com.github.tvbox.osc.bean.SourceBean;
import com.github.tvbox.osc.bean.VodInfo;
import com.github.tvbox.osc.cache.RoomDataManger;
import com.github.tvbox.osc.event.ServerEvent;
import com.github.tvbox.osc.ui.activity.CollectActivity;
import com.github.tvbox.osc.ui.activity.DetailActivity;
import com.github.tvbox.osc.ui.activity.FastSearchActivity;
import com.github.tvbox.osc.ui.activity.HistoryActivity;
import com.github.tvbox.osc.ui.activity.LivePlayActivity;
import com.github.tvbox.osc.ui.activity.LocalDataActivity;
import com.github.tvbox.osc.ui.activity.PushActivity;
import com.github.tvbox.osc.ui.activity.SearchActivity;
import com.github.tvbox.osc.ui.activity.SettingActivity;
import com.github.tvbox.osc.ui.adapter.HomeHotVodAdapter;
import com.github.tvbox.osc.util.FastClickCheckUtil;
import com.github.tvbox.osc.util.HawkConfig;
import com.github.tvbox.osc.util.ImgUtil;
import com.github.tvbox.osc.util.LocalDataHelper;
import com.github.tvbox.osc.util.LOG;
import com.github.tvbox.osc.util.UA;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.lzy.okgo.OkGo;
import com.lzy.okgo.callback.AbsCallback;
import com.lzy.okgo.model.Response;
import com.orhanobut.hawk.Hawk;
import com.owen.tvrecyclerview.widget.TvRecyclerView;
import com.owen.tvrecyclerview.widget.V7GridLayoutManager;
import com.owen.tvrecyclerview.widget.V7LinearLayoutManager;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR>
 * @date :2021/3/9
 * @description:
 */
public class UserFragment extends BaseLazyFragment implements View.OnClickListener {
    private LinearLayout tvLive;
    private LinearLayout tvSearch;
    private LinearLayout tvSetting;
    private LinearLayout tvHistory;
    private LinearLayout tvCollect;
    private LinearLayout tvPush;
    private LinearLayout tvLocalData;
    public static HomeHotVodAdapter homeHotVodAdapter;
    private List<Movie.Video> homeSourceRec;
    public static TvRecyclerView tvHotList;

    // 本地数据分页相关变量
    private int localDataCurrentPage = 1;
    private int localDataPageSize = 30;
    private boolean localDataHasMore = false;
    private boolean localDataLoading = false;

    public static UserFragment newInstance() {
        return new UserFragment();
    }

    public static UserFragment newInstance(List<Movie.Video> recVod) {
        return new UserFragment().setArguments(recVod);
    }

    public UserFragment setArguments(List<Movie.Video> recVod) {
        this.homeSourceRec = recVod;
        return this;
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        if (Hawk.get(HawkConfig.HOME_REC_STYLE, true)) {
            tvHotList.setVisibility(View.VISIBLE);
            tvHotList.setHasFixedSize(true);
            int spanCount = 5;
            if(style!=null && Hawk.get(HawkConfig.HOME_REC, 0) == 1)spanCount=ImgUtil.spanCountByStyle(style,spanCount);
            tvHotList.setLayoutManager(new V7GridLayoutManager(this.mContext, spanCount));
            int paddingLeft = getResources().getDimensionPixelSize(R.dimen.vs_15);
            int paddingTop = getResources().getDimensionPixelSize(R.dimen.vs_10);
            int paddingRight = getResources().getDimensionPixelSize(R.dimen.vs_15);
            int paddingBottom = getResources().getDimensionPixelSize(R.dimen.vs_10);
            tvHotList.setPadding(paddingLeft, paddingTop, paddingRight, paddingBottom);
        } else {
            tvHotList.setVisibility(View.VISIBLE);
            tvHotList.setLayoutManager(new V7LinearLayoutManager(this.mContext, V7LinearLayoutManager.HORIZONTAL, false));
            int paddingLeft = getResources().getDimensionPixelSize(R.dimen.vs_15);
            int paddingTop = getResources().getDimensionPixelSize(R.dimen.vs_40);
            int paddingRight = getResources().getDimensionPixelSize(R.dimen.vs_15);
            int paddingBottom = getResources().getDimensionPixelSize(R.dimen.vs_40);
            tvHotList.setPadding(paddingLeft, paddingTop, paddingRight, paddingBottom);
        }

        // 为本地数据模式优化电视导航
        if (Hawk.get(HawkConfig.HOME_REC, 0) == 3) {
            setupTvNavigationForLocalData();
        }
        if (Hawk.get(HawkConfig.HOME_REC, 0) == 2) {
            List<VodInfo> allVodRecord = RoomDataManger.getAllVodRecord(20);
            List<Movie.Video> vodList = new ArrayList<>();
            for (VodInfo vodInfo : allVodRecord) {
                Movie.Video vod = new Movie.Video();
                vod.id = vodInfo.id;
                vod.sourceKey = vodInfo.sourceKey;
                vod.name = vodInfo.name;
                vod.pic = vodInfo.pic;
                if (vodInfo.playNote != null && !vodInfo.playNote.isEmpty())
                    vod.note = "上次看到" + vodInfo.playNote;
                vodList.add(vod);
            }
            homeHotVodAdapter.setNewData(vodList);
        } else if (Hawk.get(HawkConfig.HOME_REC, 0) == 3) {
            // 本地数据模式 - 重置分页并加载第一页数据
            resetLocalDataPagination();
            loadLocalDataWithPagination(homeHotVodAdapter, true);
        }
    }

    @Override
    protected int getLayoutResID() {
        return R.layout.fragment_user;
    }

    private void jumpSearch(Movie.Video vod){
        Intent newIntent;
        if(Hawk.get(HawkConfig.FAST_SEARCH_MODE, false)){
            newIntent = new Intent(mContext, FastSearchActivity.class);
        }else {
            newIntent = new Intent(mContext, SearchActivity.class);
        }
        newIntent.putExtra("title", vod.name);
        newIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        mActivity.startActivity(newIntent);
    }
    private ImgUtil.Style style;
    @Override
    protected void init() {
        EventBus.getDefault().register(this);
//        tvLive = findViewById(R.id.tvLive);
        tvSearch = findViewById(R.id.tvSearch);
        tvSetting = findViewById(R.id.tvSetting);
        tvCollect = findViewById(R.id.tvFavorite);
        tvHistory = findViewById(R.id.tvHistory);
//        tvPush = findViewById(R.id.tvPush);
        tvLocalData = findViewById(R.id.tvLocalData);
//        tvLive.setOnClickListener(this);
        tvSearch.setOnClickListener(this);
        tvSetting.setOnClickListener(this);
        tvHistory.setOnClickListener(this);
//        tvPush.setOnClickListener(this);
        tvCollect.setOnClickListener(this);
        tvLocalData.setOnClickListener(this);
//        tvLive.setOnFocusChangeListener(focusChangeListener);
        tvSearch.setOnFocusChangeListener(focusChangeListener);
        tvSetting.setOnFocusChangeListener(focusChangeListener);
        tvHistory.setOnFocusChangeListener(focusChangeListener);
//        tvPush.setOnFocusChangeListener(focusChangeListener);
        tvCollect.setOnFocusChangeListener(focusChangeListener);
        tvLocalData.setOnFocusChangeListener(focusChangeListener);
        tvHotList = findViewById(R.id.tvHotList);
        if (Hawk.get(HawkConfig.HOME_REC, 0) == 1 && homeSourceRec!=null) {
            style=ImgUtil.initStyle();
        }
        String tvRate="";
        if(Hawk.get(HawkConfig.HOME_REC, 0) == 0){
            tvRate="豆瓣热播";
        }else if(Hawk.get(HawkConfig.HOME_REC, 0) == 1){
          tvRate= homeSourceRec!=null?"站点推荐":"豆瓣热播";
        }else if(Hawk.get(HawkConfig.HOME_REC, 0) == 3){
          tvRate="本地数据";
        }
        homeHotVodAdapter = new HomeHotVodAdapter(style,tvRate);

        // 为本地数据模式设置分页加载监听器
        if (Hawk.get(HawkConfig.HOME_REC, 0) == 3) {
            homeHotVodAdapter.setLoadMoreView(new com.github.tvbox.osc.ui.tv.widget.LoadMoreView());
            homeHotVodAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
                @Override
                public void onLoadMoreRequested() {
                    if (!localDataLoading && localDataHasMore) {
                        loadLocalDataWithPagination(homeHotVodAdapter, false);
                    }
                }
            }, tvHotList);
        }

        homeHotVodAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                Movie.Video vod = ((Movie.Video) adapter.getItem(position));
                // 对于本地数据，不需要检查SourceBeanList
                if (!"local".equals(vod.sourceKey) && ApiConfig.get().getSourceBeanList().isEmpty()) {
                    return;
                }
                
                if ((vod.id != null && !vod.id.isEmpty()) && (Hawk.get(HawkConfig.HOME_REC, 0) == 2) && HawkConfig.hotVodDelete) {
                    homeHotVodAdapter.remove(position);
                    VodInfo vodInfo = RoomDataManger.getVodInfo(vod.sourceKey, vod.id);
                    assert vodInfo != null;
                    RoomDataManger.deleteVodRecord(vod.sourceKey, vodInfo);
                    Toast.makeText(mContext, "已删除当前记录", Toast.LENGTH_SHORT).show();
               } else if (vod.id != null && !vod.id.isEmpty()) {
                    Bundle bundle = new Bundle();
                    bundle.putString("id", vod.id);
                    bundle.putString("sourceKey", vod.sourceKey);
                    bundle.putString("picture", vod.pic);
                    
                    // 特殊处理本地文件
                    if ("local".equals(vod.sourceKey)) {
                        bundle.putString("title", vod.name);
                        jumpActivity(DetailActivity.class, bundle);
                    } else {
                        SourceBean sourceBean = ApiConfig.get().getSource(vod.sourceKey);
                        if(sourceBean!=null){
                            jumpActivity(DetailActivity.class, bundle);
                        }else {
                            jumpSearch(vod);
                        }
                    }
                } else {
                    jumpSearch(vod);
                }
            }
        });
        
        homeHotVodAdapter.setOnItemLongClickListener(new BaseQuickAdapter.OnItemLongClickListener() {
            @SuppressLint("NotifyDataSetChanged")
            @Override
            public boolean onItemLongClick(BaseQuickAdapter adapter, View view, int position) {
                if (ApiConfig.get().getSourceBeanList().isEmpty()) return false;
                Movie.Video vod = ((Movie.Video) adapter.getItem(position));
                // Additional Check if : Home Rec 0=豆瓣, 1=推荐, 2=历史
                assert vod != null;
                if ((vod.id != null && !vod.id.isEmpty()) && (Hawk.get(HawkConfig.HOME_REC, 0) == 2)) {
                    HawkConfig.hotVodDelete = !HawkConfig.hotVodDelete;
                    homeHotVodAdapter.notifyDataSetChanged();
                } else {
                    Bundle bundle = new Bundle();
                    bundle.putString("title", vod.name);
                    jumpActivity(FastSearchActivity.class, bundle);                    
                }
                return true;
            }    
        });

        tvHotList.setOnItemListener(new TvRecyclerView.OnItemListener() {
            @Override
            public void onItemPreSelected(TvRecyclerView parent, View itemView, int position) {
                itemView.animate().scaleX(1.0f).scaleY(1.0f).setDuration(300).setInterpolator(new BounceInterpolator()).start();
            }

            @Override
            public void onItemSelected(TvRecyclerView parent, View itemView, int position) {
                itemView.animate().scaleX(1.05f).scaleY(1.05f).setDuration(300).setInterpolator(new BounceInterpolator()).start();
            }

            @Override
            public void onItemClick(TvRecyclerView parent, View itemView, int position) {

            }
        });
        tvHotList.setAdapter(homeHotVodAdapter);

        initHomeHotVod(homeHotVodAdapter);
    }

    private void initHomeHotVod(HomeHotVodAdapter adapter) {
        if (Hawk.get(HawkConfig.HOME_REC, 0) == 1) {
            if (homeSourceRec != null) {
                adapter.setNewData(homeSourceRec);
                return;
            }
        } else if (Hawk.get(HawkConfig.HOME_REC, 0) == 2) {
            return;
        } else if (Hawk.get(HawkConfig.HOME_REC, 0) == 3) {
            // 本地数据模式 - 重置分页并加载第一页数据
            resetLocalDataPagination();
            loadLocalDataWithPagination(adapter, true);
            return;
        }
        setDouBanData(adapter);
    }

    private void loadLocalData(HomeHotVodAdapter adapter) {
        try {
            String localPath = Hawk.get(HawkConfig.LOCAL_DATA_PATH, "");
            if (localPath.isEmpty()) {
                adapter.setNewData(new ArrayList<>());
                return;
            }

            List<Movie.Video> localVideos = LocalDataHelper.loadLocalVideos(localPath, getContext());
            adapter.setNewData(localVideos);
        } catch (Throwable th) {
            th.printStackTrace();
            adapter.setNewData(new ArrayList<>());
        }
    }

    /**
     * 重置本地数据分页状态
     */
    private void resetLocalDataPagination() {
        localDataCurrentPage = 1;
        localDataHasMore = false;
        localDataLoading = false;
    }

    /**
     * 分页加载本地数据
     * @param adapter 适配器
     * @param isFirstPage 是否是第一页
     */
    private void loadLocalDataWithPagination(HomeHotVodAdapter adapter, boolean isFirstPage) {
        if (localDataLoading) {
            return;
        }

        localDataLoading = true;

        try {
            String localPath = Hawk.get(HawkConfig.LOCAL_DATA_PATH, "");
            if (localPath.isEmpty()) {
                if (isFirstPage) {
                    adapter.setNewData(new ArrayList<>());
                }
                localDataLoading = false;
                localDataHasMore = false;
                if (!isFirstPage) {
                    adapter.loadMoreEnd();
                    adapter.setEnableLoadMore(false);
                }
                return;
            }

            LocalDataHelper.LocalVideoPageResult result = LocalDataHelper.loadLocalVideosWithPagination(
                localPath, getContext(), localDataCurrentPage, localDataPageSize);

            if (isFirstPage) {
                adapter.setNewData(result.videos);
            } else {
                adapter.addData(result.videos);
            }

            localDataHasMore = result.hasMore;

            if (result.hasMore) {
                localDataCurrentPage++;
                if (!isFirstPage) {
                    adapter.loadMoreComplete();
                    adapter.setEnableLoadMore(true);
                }
            } else {
                if (!isFirstPage) {
                    adapter.loadMoreEnd();
                    adapter.setEnableLoadMore(false);
                    if (localDataCurrentPage > 1) {
                        Toast.makeText(getContext(), "没有更多了", Toast.LENGTH_SHORT).show();
                    }
                }
            }

        } catch (Throwable th) {
            th.printStackTrace();
            if (isFirstPage) {
                adapter.setNewData(new ArrayList<>());
            } else {
                adapter.loadMoreEnd();
                adapter.setEnableLoadMore(false);
            }
            localDataHasMore = false;
        } finally {
            localDataLoading = false;
        }
    }

    /**
     * 为本地数据模式设置电视导航优化
     */
    private void setupTvNavigationForLocalData() {
        if (tvHotList == null) {
            return;
        }

        // 设置电视焦点处理
        tvHotList.setOnItemListener(new TvRecyclerView.OnItemListener() {
            @Override
            public void onItemPreSelected(TvRecyclerView parent, View itemView, int position) {
                // 预选中时的动画效果
                itemView.animate().scaleX(1.0f).scaleY(1.0f).setDuration(300).setInterpolator(new BounceInterpolator()).start();
            }

            @Override
            public void onItemSelected(TvRecyclerView parent, View itemView, int position) {
                // 选中时的动画效果
                itemView.animate().scaleX(1.05f).scaleY(1.05f).setDuration(300).setInterpolator(new BounceInterpolator()).start();

                // 检查是否需要预加载下一页
                int totalItemCount = homeHotVodAdapter.getItemCount();
                if (position >= totalItemCount - 10 && localDataHasMore && !localDataLoading) {
                    // 当滚动到倒数第10个项目时，预加载下一页
                    loadLocalDataWithPagination(homeHotVodAdapter, false);
                }
            }

            @Override
            public void onItemClick(TvRecyclerView parent, View itemView, int position) {
                // 点击事件已在adapter中处理
            }
        });

        // 设置边界按键事件处理
        tvHotList.setOnInBorderKeyEventListener(new TvRecyclerView.OnInBorderKeyEventListener() {
            @Override
            public boolean onInBorderKeyEvent(int direction, View focused) {
                if (direction == View.FOCUS_UP) {
                    // 向上导航时，焦点回到顶部按钮
                    if (tvHistory != null && tvHistory.getVisibility() == View.VISIBLE) {
                        tvHistory.requestFocus();
                        return true;
                    }
                }
                return false;
            }
        });

        // 设置选中项居中显示
        tvHotList.setSelectedItemAtCentered(true);
    }

    private void setDouBanData(HomeHotVodAdapter adapter) {
        try {
            Calendar cal = Calendar.getInstance();
            int year = cal.get(Calendar.YEAR);
            int month = cal.get(Calendar.MONTH) + 1;
            int day = cal.get(Calendar.DATE);
            String today = String.format("%d%d%d", year, month, day);
            String requestDay = Hawk.get("home_hot_day", "");
            if (requestDay.equals(today)) {
                String json = Hawk.get("home_hot", "");
                if (!json.isEmpty()) {
                    ArrayList<Movie.Video> hotMovies = loadHots(json);
                    if (hotMovies != null && hotMovies.size() > 0) {
                        adapter.setNewData(hotMovies);
                        return;
                    }
                }
            }
            String doubanUrl = "https://movie.douban.com/j/new_search_subjects?sort=U&range=0,10&tags=&playable=1&start=0&year_range=" + year + "," + year;
            OkGo.<String>get(doubanUrl)
                    .headers("User-Agent", UA.randomOne())
                    .execute(new AbsCallback<String>() {
                        @Override
                        public void onSuccess(Response<String> response) {
                            String netJson = response.body();
                            Hawk.put("home_hot_day", today);
                            Hawk.put("home_hot", netJson);
                            mActivity.runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    adapter.setNewData(loadHots(netJson));
                                }
                            });
                        }

                        @Override
                        public String convertResponse(okhttp3.Response response) throws Throwable {
                            return response.body().string();
                        }
                    });
        } catch (Throwable th) {
            th.printStackTrace();
        }
    }

    private ArrayList<Movie.Video> loadHots(String json) {
        ArrayList<Movie.Video> result = new ArrayList<>();
        try {
            JsonObject infoJson = new Gson().fromJson(json, JsonObject.class);
            JsonArray array = infoJson.getAsJsonArray("data");
            int limit = Math.min(array.size(), 25);
            for (int i = 0; i < limit; i++) {  // 改用索引循环
                JsonElement ele = array.get(i);
                JsonObject obj = ele.getAsJsonObject();
                Movie.Video vod = new Movie.Video();
                vod.name = obj.get("title").getAsString();
                vod.note = obj.get("rate").getAsString();
                if (!vod.note.isEmpty()) vod.note += " 分";
                vod.pic = obj.get("cover").getAsString()
                        + "@User-Agent=" + UA.randomOne()
                        + "@Referer=https://www.douban.com/";

                result.add(vod);
            }
        } catch (Throwable th) {

        }
        return result;
    }

    private View.OnFocusChangeListener focusChangeListener = new View.OnFocusChangeListener() {
        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            if (hasFocus)
                v.animate().scaleX(1.05f).scaleY(1.05f).setDuration(300).setInterpolator(new BounceInterpolator()).start();
            else
                v.animate().scaleX(1.0f).scaleY(1.0f).setDuration(300).setInterpolator(new BounceInterpolator()).start();
        }
    };

    @Override
    public void onClick(View v) {
    	
    	// takagen99: Remove Delete Mode
        HawkConfig.hotVodDelete = false;
    
        FastClickCheckUtil.check(v);
        if (v.getId() == R.id.tvLive) {
            if(Hawk.get(HawkConfig.LIVE_GROUP_LIST,new JsonArray()).isEmpty()){
                Toast.makeText(mContext, "直播源为空", Toast.LENGTH_SHORT).show();
            }else {
                jumpActivity(LivePlayActivity.class);
            }
        } else if (v.getId() == R.id.tvSearch) {
            jumpActivity(SearchActivity.class);
        } else if (v.getId() == R.id.tvSetting) {
            jumpActivity(SettingActivity.class);
        } else if (v.getId() == R.id.tvHistory) {
            jumpActivity(HistoryActivity.class);
        }
//        else if (v.getId() == R.id.tvPush) {
//            jumpActivity(PushActivity.class);
//        }
        else if (v.getId() == R.id.tvFavorite) {
            jumpActivity(CollectActivity.class);
        } else if (v.getId() == R.id.tvLocalData) {
            jumpActivity(LocalDataActivity.class);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void server(ServerEvent event) {
        if (event.type == ServerEvent.SERVER_CONNECTION) {
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}