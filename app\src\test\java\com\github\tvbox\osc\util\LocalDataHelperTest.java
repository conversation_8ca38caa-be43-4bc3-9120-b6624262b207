package com.github.tvbox.osc.util;

import android.content.Context;
import com.github.tvbox.osc.bean.Movie;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.File;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 本地数据助手分页功能测试
 */
@RunWith(MockitoJUnitRunner.class)
public class LocalDataHelperTest {

    @Mock
    private Context mockContext;

    @Test
    public void testLocalVideoPageResult() {
        // 测试LocalVideoPageResult类的基本功能
        LocalDataHelper.LocalVideoPageResult result = new LocalDataHelper.LocalVideoPageResult();
        
        // 验证默认值
        assertEquals(1, result.currentPage);
        assertEquals(1, result.totalPages);
        assertEquals(0, result.totalCount);
        assertEquals(30, result.pageSize);
        assertFalse(result.hasMore);
        assertNotNull(result.videos);
        assertTrue(result.videos.isEmpty());
    }

    @Test
    public void testPaginationCalculation() {
        // 模拟分页计算逻辑
        int totalCount = 95; // 总共95个视频
        int pageSize = 30;   // 每页30个
        
        // 计算总页数
        int expectedTotalPages = (int) Math.ceil((double) totalCount / pageSize); // 应该是4页
        assertEquals(4, expectedTotalPages);
        
        // 测试第一页
        int page1StartIndex = (1 - 1) * pageSize; // 0
        int page1EndIndex = Math.min(page1StartIndex + pageSize, totalCount); // 30
        assertEquals(0, page1StartIndex);
        assertEquals(30, page1EndIndex);
        
        // 测试第二页
        int page2StartIndex = (2 - 1) * pageSize; // 30
        int page2EndIndex = Math.min(page2StartIndex + pageSize, totalCount); // 60
        assertEquals(30, page2StartIndex);
        assertEquals(60, page2EndIndex);
        
        // 测试第三页
        int page3StartIndex = (3 - 1) * pageSize; // 60
        int page3EndIndex = Math.min(page3StartIndex + pageSize, totalCount); // 90
        assertEquals(60, page3StartIndex);
        assertEquals(90, page3EndIndex);
        
        // 测试第四页（最后一页）
        int page4StartIndex = (4 - 1) * pageSize; // 90
        int page4EndIndex = Math.min(page4StartIndex + pageSize, totalCount); // 95
        assertEquals(90, page4StartIndex);
        assertEquals(95, page4EndIndex);
        
        // 测试hasMore逻辑
        assertTrue(1 < expectedTotalPages); // 第一页有更多
        assertTrue(2 < expectedTotalPages); // 第二页有更多
        assertTrue(3 < expectedTotalPages); // 第三页有更多
        assertFalse(4 < expectedTotalPages); // 第四页没有更多
    }

    @Test
    public void testEmptyPath() {
        // 测试空路径的情况
        LocalDataHelper.LocalVideoPageResult result = 
            LocalDataHelper.loadLocalVideosWithPagination("", mockContext, 1, 30);
        
        assertNotNull(result);
        assertEquals(0, result.totalCount);
        assertEquals(1, result.totalPages);
        assertEquals(1, result.currentPage);
        assertFalse(result.hasMore);
        assertTrue(result.videos.isEmpty());
    }

    @Test
    public void testNullPath() {
        // 测试null路径的情况
        LocalDataHelper.LocalVideoPageResult result = 
            LocalDataHelper.loadLocalVideosWithPagination(null, mockContext, 1, 30);
        
        assertNotNull(result);
        assertEquals(0, result.totalCount);
        assertEquals(1, result.totalPages);
        assertEquals(1, result.currentPage);
        assertFalse(result.hasMore);
        assertTrue(result.videos.isEmpty());
    }

    @Test
    public void testPageSizeValidation() {
        // 测试不同的页面大小
        int[] pageSizes = {10, 20, 30, 50, 100};
        int totalCount = 95;
        
        for (int pageSize : pageSizes) {
            int expectedTotalPages = (int) Math.ceil((double) totalCount / pageSize);
            
            // 验证计算是否正确
            if (pageSize == 10) assertEquals(10, expectedTotalPages);
            if (pageSize == 20) assertEquals(5, expectedTotalPages);
            if (pageSize == 30) assertEquals(4, expectedTotalPages);
            if (pageSize == 50) assertEquals(2, expectedTotalPages);
            if (pageSize == 100) assertEquals(1, expectedTotalPages);
        }
    }

    @Test
    public void testPageBoundaries() {
        // 测试边界情况
        int totalCount = 30; // 正好一页的数据
        int pageSize = 30;
        
        int totalPages = (int) Math.ceil((double) totalCount / pageSize);
        assertEquals(1, totalPages);
        
        // 第一页应该包含所有数据
        int startIndex = (1 - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, totalCount);
        assertEquals(0, startIndex);
        assertEquals(30, endIndex);
        
        // 不应该有更多页
        assertFalse(1 < totalPages);
    }
}
